import { useState } from "react";
import { Outlet } from "react-router-dom";
import posthog from "posthog-js";
import GoogleTranslate from "./components/navbar/components/GoogleTranslate";

function App() {
  posthog.init("phc_3fQmBanpGrx91Ik4ZFcZQXGUmU5Pi58vfdrwG91Ybzi", {
    api_host: "https://app.posthog.com"
  });

  return (
    <div>
      <Outlet />
      {/* <GoogleTranslate /> */}
    </div>
  );
}

export default App;
