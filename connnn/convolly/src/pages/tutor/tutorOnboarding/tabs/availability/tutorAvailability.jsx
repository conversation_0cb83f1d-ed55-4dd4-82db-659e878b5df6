import React, { useState } from "react";
import { CustomSelect } from "@/components/select/select";
import { Button } from "@/components/button/button";
import { useSelector } from "react-redux";
import { useForm } from "react-hook-form";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import Loader from "@/components/loader/loader";

const TutorAvailability = ({ setActiveTab, instructorDetails }) => {
	const tutorId = useSelector((state) => state?.app?.userInfo?.user?.id);

	const {
		register,
		control,
		handleSubmit,
		setValue,
		formState: { errors },
	} = useForm();

	const timezones = [
		{
			label: "18:03 (GMT+0) - Africa/Lagos",
			value: "Africa/Lagos",
		},
		{
			label: "13:03 (GMT+0) - America/New York",
			value: "America/New_York",
		},
		{
			label: "18:03 (GMT+0) - Europe/London",
			value: "Europe/London",
		},
		{
			label: "22:33 (GMT+0) - Asia/Kolkata",
			value: "Asia/Kolkata",
		},
		{
			label: "02:03 (GMT+0) - Asia/Tokyo",
			value: "Asia/Tokyo",
		},
		{
			label: "03:03 (GMT+0) - Australia/Sydney",
			value: "Australia/Sydney",
		},
	];

	const daysOfWeek = [
		"Monday",
		"Tuesday",
		"Wednesday",
		"Thursday",
		"Friday",
		"Saturday",
		"Sunday",
	];

	// Fill time options in 30-minute intervals in 24-hour format
	const timeOptions = Array.from({ length: 48 }, (_, i) => {
		const hours = Math.floor(i / 2);
		const minutes = i % 2 === 0 ? "00" : "30";
		const time24 = `${String(hours).padStart(2, "0")}:${minutes}`;
		return { label: time24, value: time24 };
	});

	const [availability, setAvailability] = useState({});

	const { handlePost: handleUpdateTutor, isLoading: updating } = usePost(
		useUpdateProfileMutation
	);

	// If a day is checked, create time slots if that day isnt already in the available days selected. Otherwise remove it
	const toggleDay = (day) => {
		setAvailability((prev) => ({
			...prev,
			[day]: prev[day] ? undefined : { from: "", to: "" },
		}));
	};

	// type is "from" or "to"
	const updateTime = (day, type, value) => {
		setAvailability((prev) => ({
			...prev,
			[day]: {
				...prev[day],
				[type]: value,
			},
		}));
	};

	const updateAvailability = async (formData) => {
		const selectedDays = Object.keys(formData).filter((key) =>
			daysOfWeek.includes(key)
		);

		const daysAvailable = selectedDays;

		const timeAvailable = selectedDays.map((day) => {
			const from = formData[day]?.from;
			const to = formData[day]?.to;

			return {
				label: day, // Use day of the week as the label
				from,
				to,
			};
		});

		const payload = {
			userId: tutorId,
			role: "tutor",
			daysAvailable,
			timeAvailable,
			location: { address: formData?.location },
		};

		console.log("Submitting Payload:", payload);

		const res = await handleUpdateTutor(payload);

		if (res) {
			setActiveTab("Price");
		}
	};

	console.log(errors);
	console.log(instructorDetails?.timeAvailable);

	return (
		<>
			{updating && <Loader />}

			<form
				className="max-w-[686px] w-[93%] mx-auto"
				onSubmit={handleSubmit(updateAvailability)}
			>
				<h2 className="sm:text-4xl text-3xl font-bold mb-5 sm:mt-8 lg:mt-0">
					Set your time zone
				</h2>
				<p className="text-[#4B5563] sm:text-lg mb-8">
					A correct time zone is essential to coordinate lessons with
					international students
				</p>

				<CustomSelect
					placeholder="13:03 (GMT+0) - America/New York"
					label="Choose your time zone"
					options={timezones}
					control={control}
					name="location"
					isRequired={true}
					error={errors?.location?.message}
					className="p-5 py-[22px]"
					parentClassName="mb-7"
				/>

				<h3 className="mb-3 sm:text-xl text-lg font-bold">
					Set your availability
				</h3>

				<p className="text-[#4B5563] sm:text-lg mb-8">
					Availability shows your potential working hours. Students can book
					lessons at these times
				</p>

				<div className="space-y-4 mb-10">
					{daysOfWeek.map((day) => (
						<div key={day}>
							<label className="flex items-center gap-2 w-fit mb-2">
								<input
									type="checkbox"
									checked={!!availability[day]}
									onChange={() => toggleDay(day)}
									className="custom-checkbox text-sm border border-[#E8E8E8] bg-white rounded-xl focus:outline-none"
								/>
								<span className="sm:text-xl font-bold text-lg">{day}</span>
							</label>

							{availability[day] && (
								<div className="flex gap-4 w-full mb-5">
									<CustomSelect
										options={timeOptions}
										placeholder="From"
										label="From"
										control={control}
										name={`${day}.from`}
										isRequired={true}
										error={errors?.from?.message}
										className="p-5 py-[22px]"
									/>

									<CustomSelect
										options={timeOptions}
										placeholder="To"
										label="To"
										control={control}
										name={`${day}.to`}
										isRequired={true}
										error={errors?.to?.message}
									/>
								</div>
							)}
						</div>
					))}
				</div>

				<div className="sm:flex gap-5">
					<Button
						className="w-full h-[50px] bg-white border-[#D2D2D2] text-secondary border hover:bg-white"
						onClick={() => setActiveTab("Video")}
					>
						Back
					</Button>

					<Button className="w-full h-[50px]" disabled={updating} type="submit">
						Save And Continue
					</Button>
				</div>
			</form>
		</>
	);
};

export default TutorAvailability;
