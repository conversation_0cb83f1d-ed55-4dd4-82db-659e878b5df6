import React, { useState, useEffect } from "react";
import tutorAvatar from "@/assets/images/tutorAvatar.png";
import tutorPictureExample1 from "@/assets/images/tutorPictureExample1.png";
import tutorPictureExample2 from "@/assets/images/tutorPictureExample2.png";
import tutorPictureExample3 from "@/assets/images/tutorPictureExample3.png";
import tutorPictureExample4 from "@/assets/images/tutorPictureExample4.png";
import usa from "@/assets/svgs/usa.svg";
import lessons from "@/assets/svgs/lessons.svg";
import greenTick from "@/assets/svgs/greenTick.svg";
import uploadIcon from "@/assets/svgs/uploadIcon.svg";
import { Button } from "@/components/button/button";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import { useForm } from "react-hook-form";
import { useSelector } from "react-redux";
import { capitalizeWords } from "@/utils/utils";
import { convertToBase64 } from "@/utils/utils";
import Loader from "@/components/loader/loader";


const TutorPhoto = ({ setActiveTab, instructorDetails }) => {
  const tutorId = useSelector((state) => state?.app?.userInfo?.user?.id);
  const {
    register,
    control,
    handleSubmit,
    setValue,
    getValues,
    formState: { errors }
  } = useForm();


  console.log(errors)

  const { handlePost: handleUpdateTutor, isLoading: updating } = usePost(
    useUpdateProfileMutation
  );

  const [previewImage, setPreviewImage] = useState(null);

  const updateImage = async () => {
    if (previewImage) {
      const res = await handleUpdateTutor({
        image: previewImage,
        userId: tutorId,
        role: "tutor"
      });

      if (res) {
        setActiveTab("Proficiency");
      }
    }
  };

  useEffect(() => {
    if (instructorDetails) {
      setPreviewImage(instructorDetails?.image);
    }
  }, [instructorDetails, setValue]);

  console.log(getValues('image'))

  return (
    <>
      {updating && <Loader />}

    <form
      className="max-w-[528px] w-[93%] mx-auto"
      onSubmit={handleSubmit(updateImage)}
    >
      <h2 className="sm:text-4xl text-3xl font-bold mb-5 sm:mt-8 lg:mt-0">
        Upload a photo
      </h2>
      <p className="text-[#4B5563] sm:text-lg mb-8">
        Choose a photo that will help learners get to know you
      </p>

      <div className="flex sm:gap-5 gap-2 mb-7">
        <img
          src={previewImage || tutorAvatar}
          alt="Tutor Preview"
          className="w-full sm:max-w-[170px] max-w-[100px] h-full max-h-[150px] object-cover rounded-md border"
        />

        <div className="flex flex-col">
          <div className="flex items-center mb-5 gap-2">
            <h3 className="sm:text-xl text-lg font-bold">
              {capitalizeWords(
                instructorDetails?.firstname + " " + instructorDetails?.lastname
              )}
            </h3>
            {/* <img src={usa} alt="country icon" className="w-5 h-5" /> */}
          </div>

          <div className="flex items-center gap-1 mb-2 mt-auto">
            <img src={lessons} alt="lessons icon" />
            <span className="text-[#4B5563] mr-5">Teaches English lesson</span>
          </div>

          <div className="flex items-center gap-1">
            <span className="text-[#4B5563]">English: </span>
            <span className="text-black mr-5">Native</span>

            <span className="text-[#4B5563]">
              {instructorDetails?.languages[0]?.name}
            </span>
           
          </div>
        </div>
      </div>

      <label
        className="w-full border border-dashed border-[#D2D2D2] sm:p-5 p-3 flex flex-col items-center rounded-lg cursor-pointer mb-7"
        htmlFor="upload-image"
      >
        <img src={uploadIcon} alt="upload icon" />
        <p className="text-[#4B5563] sm:text-lg mb-2">
          Choose a file or drag and drop it here
        </p>
        <p className="text-[#4B5563] sm:text-lg mb-5">Jpeg, Png, Max 5mb</p>

        <label className="text-primary font-bold sm:text-xl text-lg">
          Browse
        </label>

        <input
          type="file"
          aria-label="upload a picture"
          id="upload-image"
          {...register("image", {
            required: !previewImage ? "Please upload an image" : false,
            onChange: async (e) => {
              const file = e.target.files?.[0];
              if (file) {
                const base64 = await convertToBase64(file);
                setPreviewImage(base64);
              }
            }
          })}
          accept="image/png, image/jpeg"
          className="hidden"
        />
      </label>

      <p className="sm:text-xl text-lg mb-5 font-bold">
        What your photo should look like
      </p>

      <div className="grid sm:grid-cols-4 grid-cols-2 gap-3 mb-5">
        <img
          src={tutorPictureExample1}
          alt="example 1"
          className="h-[100px] object-cover rounded-lg w-full"
        />
        <img
          src={tutorPictureExample2}
          alt="example 2"
          className="h-[100px] object-cover rounded-lg w-full"
        />
        <img
          src={tutorPictureExample3}
          alt="example 4"
          className="h-[100px] object-cover rounded-lg w-full"
        />
        <img
          src={tutorPictureExample4}
          alt="example 3"
          className="h-[100px] object-cover rounded-lg w-full"
        />
      </div>

      {[
        "You should be facing forward",
        "You should be centered and upright",
        "You should be the only person in the photo",
        "Use a color photo with high resolution and no filters",
        "Frame your head and shoulders"
      ].map((text, idx) => (
        <div key={idx} className="flex gap-3 items-center mb-3">
          <img src={greenTick} alt="green tick" />
          <p className="text-[#4B5563] max-sm:text-sm">{text}</p>
        </div>
      ))}

      <div className="sm:flex gap-5 mt-10">
        <Button
          className="w-full h-[50px] bg-white border-[#D2D2D2] text-secondary border hover:bg-white"
          onClick={() => setActiveTab("About")}
        >
          Back
        </Button>

        <Button className="w-full h-[50px]" type="submit" disabled={updating}>
          Save And Continue
        </Button>
      </div>
    </form>
    </>


  );
};

export default TutorPhoto;
