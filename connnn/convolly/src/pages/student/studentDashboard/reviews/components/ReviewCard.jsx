import React, { useState } from "react";
import img from "../../../../../assets/images/tutor1.png";
import StarRating from "../../../../../assets/images/studentDashboard/Star 5.png";
import ReviewButton from "./ReviewButton";

const tutor = {
	name: "<PERSON>",
	totalReview: 6,
	avatar: img,
	courseTitle: "Grammar and Vocabulary focus",
	ratingDate: "12/23/2023",
};

const ReviewCard = () => {
	const [showOptions, setShowOptions] = useState(false);

	const handleShowOptions = () => {
		setShowOptions(true);
		console.log("button clicked");
	};

	const hideOption = () => {
		setShowOptions(false);
	};

	return (
		<div className="my-2 pb-2 border-b">
			{/* Desktop Layout - Keep as is */}
			<div className="hidden sm:flex justify-between flex-row pr-4">
				<div className="flex justify-between flex-row">
					<div className="flex pr-6 w-[220px]">
						<img
							src={tutor.avatar}
							alt=""
							className="object-cover w-16 h-16 mr-6 rounded-md"
						/>
						<div className="mt-1">
							<p className="text-[#1A1A40] text-md font-semibold">
								{tutor.name}
							</p>
							<div className="flex flex-row">
								<p className="text-sm text-[#4B5563]">
									Total Review: {tutor.totalReview}
								</p>
							</div>
						</div>
					</div>

					{/* rating */}
					<div className="flex flex-col w-[450px]">
						<div className="flex gap-5 flex-row py-2">
							<p className="text-[#1A1A40] text-md flex font-semibold">
								<img src={StarRating} alt="" />
								<img src={StarRating} alt="" />
								<img src={StarRating} alt="" />
								<img src={StarRating} alt="" />
							</p>
							<p className="text-[16px] text-[#4B5563]">{tutor.ratingDate}</p>
						</div>

						<p className="text-[#4B5563] text-sm w-100">
							Start creating your public tutor profile. Your progress will be
							automatically saved as you complete each section. You can return
							at any time to finish your registration.
						</p>
					</div>
				</div>

				<div onClick={(e) => e.stopPropagation()} className="relative">
					<ReviewButton />
				</div>
			</div>

			{/* Mobile Layout */}
			<div className="block sm:hidden">
				<div className="flex items-start justify-between mb-3">
					<div className="flex items-start">
						<img
							src={tutor.avatar}
							alt=""
							className="object-cover w-12 h-12 mr-3 rounded-md flex-shrink-0"
						/>
						<div className="min-w-0 flex-1">
							<p className="text-[#1A1A40] text-sm font-semibold leading-tight">
								{tutor.name}
							</p>
							<p className="text-xs text-[#4B5563]">
								Total Review: {tutor.totalReview}
							</p>
						</div>
					</div>
					<div className="relative">
						<ReviewButton />
					</div>
				</div>

				{/* Rating and date */}
				<div className="flex items-center justify-between mb-2">
					<div className="flex items-center">
						<img src={StarRating} alt="" className="w-4 h-4" />
						<img src={StarRating} alt="" className="w-4 h-4" />
						<img src={StarRating} alt="" className="w-4 h-4" />
						<img src={StarRating} alt="" className="w-4 h-4" />
					</div>
					<p className="text-xs text-[#4B5563]">{tutor.ratingDate}</p>
				</div>

				{/* Review text */}
				<p className="text-[#4B5563] text-xs leading-relaxed">
					Start creating your public tutor profile. Your progress will be
					automatically saved as you complete each section. You can return at
					any time to finish your registration.
				</p>
			</div>
		</div>
	);
};

export default ReviewCard;
