import { <PERSON><PERSON> } from "@/components/button/button";
import { X } from "lucide-react";
import React from "react";
import img from "../../../../../assets/images/tutor1.png";
import StarRating from "../../../../../assets/images/studentDashboard/Star 5.png";
import { Star } from "lucide-react";

const tutor = {
	name: "<PERSON>",
	totalReview: 6,
	avatar: img,
	courseTitle: "Grammar and Vocabulary focus",
	ratingDate: "12/23/2023",
};

const EditReviewModal = ({ onClose }) => {
	return (
		<div>
			<div className="sm:w-[600px] w-sm mx-auto p-3 sm:p-6 bg-white rounded-lg shadow-lg">
				<div className="flex justify-between items-center border-gray-200">
					<p className="text-2xl font-bold text-[#1A1A40]"></p>
					<button
						onClick={onClose}
						className="text-gray-500 hover:text-gray-700"
					>
						<X size={30} />
					</button>
				</div>

				<div className="mt-6">
					<div className="flex">
						<div className="flex pr-6 w-[220px] ">
							<img
								src={tutor.avatar}
								alt=""
								className="object-cover w-16 h-16 mr-6 rounded-md"
							/>
							<div className="mt-1">
								<p className="text-[#1A1A40] text-[22px] font-semibold">
									{tutor.name}
								</p>
								<div className="flex flex-row">
									<p className="text-sm text-[#4B5563] ">
										{" "}
										Total Review: {tutor.totalReview}
									</p>
								</div>
							</div>
						</div>
						<div className="flex gap-5 flex-row pr-4">
							<p className="text-[#1A1A40] text-md flex font-semibold">
								{[...Array(5)].map((_, i) => (
									<img
										key={i}
										src={StarRating}
										alt="Star"
										className="w-6 h-6"
									/>
								))}
							</p>
						</div>
					</div>
					<br />
					<div>
						<div className="mt-6">
							<label className="block text-[#1A1A40] text-md font-medium mb-2">
								Comment
							</label>
							<textarea
								className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								rows={6}
								placeholder="Write your review here..."
							/>
						</div>
					</div>
					<Button className="mt-4 w-full h-[50px]">Submit review</Button>
				</div>
			</div>
		</div>
	);
};

export default EditReviewModal;
