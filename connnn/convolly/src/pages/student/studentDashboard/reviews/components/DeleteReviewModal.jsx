import { But<PERSON> } from "@/components/button/button";
import { X } from "lucide-react";
import React from "react";

const DeleteReviewModal = ({ onClose }) => {
	return (
		<div>
			<div className="sm:w-[600px] w-sm mx-auto p-3 sm:p-6 bg-white rounded-lg shadow-lg">
				<div className="flex justify-between items-center pb-4 border-b border-gray-200">
					<p className="text-2xl font-bold text-[#1A1A40]"></p>
					<button
						onClick={onClose}
						className="text-gray-500 hover:text-gray-700"
					>
						<X />
					</button>
				</div>

				<div className="mt-6">
					<div className="mb-8">
						<h1 className="text-[22px] text-[#1A1A40] font-semibold text-center">
							Are you sure you want to delete review ?
						</h1>
					</div>
					<div className="flex items-center justify-center gap-4">
						<div>
							<Button
								onClick={onClose}
								className="flex-1 h-[50px] w-40 bg-white border text-black hover:text-white rounded-md "
							>
								No
							</Button>
						</div>
						<div>
							<Button
								onClick={onClose}
								className="flex-1 h-[50px] w-40 bg-red-600  text-white"
							>
								Yes, Delete
							</Button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default DeleteReviewModal;
