import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./app.jsx";
import { store } from "./redux/store.js";
import { Provider } from "react-redux";
import { appRoutes } from "./_config/inAppUrl.jsx";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import { loadStripe } from '@stripe/stripe-js';
import { ToastProvider } from "./context/toastContext/toastContext";
import { Elements } from '@stripe/react-stripe-js';

// Initialize Stripe with your publishable key
const stripePromise = loadStripe(
  "pk_live_51RWDv2BuUzltG5tvKHFIpQW0QA2QQyW3ZEnSJLyFXXkY3yj2Lnq863SMR1GdPZmA5ZHeyRvAmsdNbmK1sc84RwMY00J9JsQ0pu"
);

const routerConfig = createBrowserRouter([
  {
    path: "/",
    element: <App />,
    children: appRoutes
  }
]);

const RootApp = () => {
  return <RouterProvider router={routerConfig} />;
};

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <Provider store={store}>
      <ToastProvider>
        <Elements stripe={stripePromise}>
          <RootApp />
        </Elements>
      </ToastProvider>
    </Provider>
  </StrictMode>
);
