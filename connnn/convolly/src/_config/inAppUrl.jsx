import LoginPage from "../pages/auth/login";
import CreatePassword from "../pages/auth/passwordManager/CreatePassword";
import ForgotPassword from "../pages/auth/passwordManager/ForgotPassword";
import VerifyPassword from "../pages/auth/passwordManager/VerifyPassword";
import { HomePage } from "../pages/home/<USER>";
import SignupPage from "../pages/auth/signup";
import FindTutors from "../pages/findTutors/findTutors";
import TutorOnboarding from "../pages/tutor/tutorOnboarding/tutorOnboardingLayout";
import StudentOnboarding from "@/pages/student/studentOnboarding/studentOnboardingLayout";
import StudentDashboard from "@/pages/student/studentDashboard/studentDashboard";
import TutorDashboard from "@/pages/tutor/tutorDashboard/tutorDashboard";
import ClassroomLayout from "@/pages/classroom/classroomLayout";
import TutorProfile from "@/pages/tutor/tutorProfile/tutorProfile";
import Payments from "@/pages/payments/Payments";
import PostPayment from "@/pages/payments/PostPayment";
import LessonTrial from "@/pages/lessonTrial/LessonTrial";
import MyLessons from "@/pages/student/studentDashboard/mylessons/MyLessons";
import Tutors from "@/pages/student/studentDashboard/mylessons/tutors/Tutors";
import Calender from "@/pages/student/studentDashboard/mylessons/calendar/Calender";
import StudentReviews from "@/pages/student/studentDashboard/reviews/StudentReviews";
import StudentSettings from "@/pages/student/studentDashboard/settings/StudentSettings";
import TutorDetails from "@/pages/student/studentDashboard/mylessons/tutors/TutorDetails";
import StudentLayoutWrapper from "@/layout/studentLayoutWrapper/StudentLayoutWrapper";
import SubscriptionPage from "@/pages/student/studentDashboard/mylessons/tutors/SubscriptionPage";
import TutorLayoutWrapper from "@/layout/tutorLayoutWrapper/TutorLayoutWrapper";
import TutorLessons from "@/pages/tutor/tutorDashboard/mylessons/TutorLessons";
import TutorReviews from "@/pages/tutor/tutorDashboard/reviews/TutorReviews";
import TutorSettings from "@/pages/tutor/tutorDashboard/settings/TutorSettings";
import AdminDashboard from "@/pages/admin/adminDashboard/AdminDashboard";
import AdminLayoutWrapper from "@/layout/adminLayoutWrapper/AdminLayoutWrapper";
import AdminStudentsPage from "@/pages/admin/adminDashboard/AdminStudentsPage";
import AdminStudentProfile from "@/pages/admin/adminDashboard/AdminStudentProfile";
import AdminTutorProfile from "@/pages/admin/adminDashboard/AdminTutorProfile";
import AdminTutorsPage from "@/pages/admin/adminDashboard/AdminTutorsPage";
import AdminHistory from "@/pages/admin/adminDashboard/AdminHistory";
import AdminBookings from "@/pages/admin/adminDashboard/AdminBookings";
import AdminPayouts from "@/pages/admin/adminDashboard/AdminPayouts";
import AdminSubAccounts from "@/pages/admin/adminDashboard/AdminSubAccounts";
import AdminRefundApproval from "@/pages/admin/adminDashboard/AdminRefundApproval";
import SystemSsettings from "@/pages/admin/adminDashboard/SystemSsettings";
import AdminReviews from "@/pages/admin/adminDashboard/AdminReviews";
import AdminReport from "@/pages/admin/adminDashboard/AdminReport";
import AdminApplications from "@/pages/admin/adminDashboard/AdminApplications";
import AdminTutorsReviews from "@/pages/admin/components/tutors/AdminTutorsReviews";
import Subscription from "@/pages/student/studentDashboard/subscription/Subscription";
import Chats from "@/pages/messaging/Chats";

export const appRoutes = [
  {
    path: "/",
    element: <HomePage />,
  },
  {
    path: "/signin",
    element: <LoginPage />,
  },
  {
    path: "/reset-password",
    element: <ForgotPassword />,
  },
  {
    path: "create-password",
    element: <CreatePassword />,
  },
  {
    path: "/verify-password/",
    element: <VerifyPassword />,
  },
  {
    path: "/signup/:role",
    element: <SignupPage />,
  },
  {
    path: "/find-tutors",
    element: <FindTutors />,
  },
  {
    path: "/tutor-onboarding",
    element: <TutorOnboarding />,
  },
  {
    path: "/student-onboarding",
    element: <StudentOnboarding />,
  },
  {
    path: "/tutor-profile/:id",
    element: <TutorProfile />,
  },

  {
    path: "/classroom/:classId",
    element: <ClassroomLayout />,
  },
  {
    path: "/admin",
    element: <AdminLayoutWrapper />,
    children: [
      {
        path: "dashboard",
        element: <AdminDashboard />,
      },
      {
        path: "students",
        element: <AdminStudentsPage />,
      },
      {
        path: "students/:id",
        element: <AdminStudentProfile />,
      },
      {
        path: "tutors",
        element: <AdminTutorsPage />,
      },
      {
        path: "tutors/:id",
        element: <AdminTutorProfile />,
      },
      {
        path: "tutors/:id/reviews",
        element: <AdminTutorsReviews />,
      },
      {
        path: "history",
        element: <AdminHistory />,
      },
      {
        path: "bookings",
        element: <AdminBookings />,
      },
      {
        path: "payouts",
        element: <AdminPayouts />,
      },
      {
        path: "refund-approval",
        element: <AdminRefundApproval />,
      },
      {
        path: "subaccounts",
        element: <AdminSubAccounts />,
      },

      {
        path: "reports",
        element: <AdminReport />,
      },
      {
        path: "applications",
        element: <AdminApplications />,
      },
    ],
  },
  {
    path: "/tutor",
    element: <TutorLayoutWrapper />,
    children: [
      {
        path: "dashboard",
        element: <TutorDashboard />,
      },
      {
        path: "messages/:userId?",
        element: <Chats />,
      },
      {
        path: "my-lessons",
        element: <TutorLessons />,
      },
      {
        path: "reviews",
        element: <TutorReviews />,
      },
      {
        path: "settings",
        element: <TutorSettings />,
      },
    ],
  },

  {
    path: "/student",
    element: <StudentLayoutWrapper />,
    children: [
      {
        path: "dashboard",
        element: <StudentDashboard />,
      },
      {
        path: "messages/:userId?",
        element: <Chats />,
      },
      {
        path: "my-lessons",
        element: <MyLessons />,
      },
      {
        path: "tutors",
        element: <Tutors />,
      },
      {
        path: "my-lessons/tutors/:id",
        element: <TutorDetails />,
      },
      {
        path: "my-lessons/tutors/:id/subscribe",
        element: <SubscriptionPage />,
      },
      {
        path: "calendar",
        element: <Calender />,
      },
      {
        path: "reviews",
        element: <StudentReviews />,
      },
      {
        path: "subscription",
        element: <Subscription />,
      },
      {
        path: "settings",
        element: <StudentSettings />,
      },
    ],
  },
  {
    path: "/payment",
    element: <Payments />,
  },
  {
    path: "/post-payment",
    element: <PostPayment />,
  },
  {
    path: "/trial-lesson",
    element: <LessonTrial />,
  },
];
