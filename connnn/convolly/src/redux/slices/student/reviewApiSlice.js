import { generalApiSlice } from "../../apiSlice";

const reviewApiSlice = generalApiSlice.injectEndpoints({
	endpoints: (builder) => ({
		getMyReviewsOnTutor: builder.query({
			query: ({ id, page, cursor, limit = 10, sortOrder = "desc" }) => {
				const params = new URLSearchParams();

				if (page) params.append("page", page);
				if (cursor) params.append("cursor", cursor);
				if (limit) params.append("limit", limit);
				if (sortOrder) params.append("sortOrder", sortOrder);

				return {
					url: `/api/reviews/by-me/${id}?${params.toString()}`,
					method: "POST",
				};
			},
		}),

		reviewTutor: builder.mutation({
			query: (body) => ({
				url: `/api/reviews/create/${body.id}`,
				method: "POST",
				body,
			}),
		}),

		updateReview: builder.mutation({
			query: (body) => ({
				url: `/api/reviews/create/${body.id}`,
				method: "PATCH",
			}),
		}),

		deleteReview: builder.mutation({
			query: (body) => ({
				url: `/api/reviews/create/${body.id}`,
				method: "DELETE",
			}),
		}),
	}),

	overrideExisting: false,
});

export const {
	useGetMyReviewsOnTutorQuery,
	useUpdateReviewMutation,
	useDeleteReviewMutation,
	useReviewTutorMutation,
} = reviewApiSlice;
