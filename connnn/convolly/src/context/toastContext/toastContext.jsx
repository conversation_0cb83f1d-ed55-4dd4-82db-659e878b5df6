import React from 'react'
import { ToastContainer, toast } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'

export const ToastProvider = ({ children }) => {
  return (
    <>
      <ToastContainer />
      {children}
    </>
  )
}

export const useToast = () => {
  const showSuccess = message => toast.success(message)
  const showError = message => toast.error(message)
  const showLoading = message => toast.loading(message)
  const hideLoading = message => toast.dismiss(message)

  return {
    showSuccess,
    showError,
    showLoading,
    hideLoading
  }
}
